# Opdrachtomschrijving Semester 2: De Secure Infrastructure

## Introductie
Welkom bij **Secure Infrastructure**, de opdracht voor semester 2 van de leerroute Cyber Security. Tijdens deze opdracht werk je in teams aan de ontwikkeling van een applicatie en bijbehorende infrastructuur voor een bedrijf in Nederland. In het project richt je je niet alleen op de technische vaardigheden, maar ook op professionele vaardigheden, zoals samen<PERSON>, communiceren, plannen, en onderzoek doen.

Dit document geeft je een overzicht van de context, de deliverables, de werkwijze en de leeruitkomsten. Naast dit document zijn er nog een aantal belangrijke start-documenten:

- De [Project Brief](docs/getting-started/project_brief.md): hierin staat de volledige opdracht van de opdrachtgever.  
- De [Product Requirements](docs/getting-started/product_requirements.md): hierin vind je de functionele en technische vereisten.  
- De [Sprint Goals](docs/getting-started/sprint_goals.md): hierin sta<PERSON> de doele<PERSON> van de eerste drie sprints, inclusief een (beknopt) overzicht van de doelen per product.

---

## Context
Jullie gaan met je team aan de slag bij **ALSSM Inc.**[^1], een fictief bedrijf dat zich specialiseert in het ontwerpen van veilige IT-oplossingen. Jullie werken aan projecten binnen allerhande sectoren in Nederland, van retail tot transport, van zorg tot financieel.

Jullie applicatie gaat een zelf-gekozen bedrijf in deze sector ondersteunen bij een **mission-critical activity**. Omdat deze activiteiten belangrijk zijn bij het uitvoeren van de kerntaken van de organisatie, is het uiteraard van belang om een **secure by design**-mindset te hanteren: vanaf het allereerste begin staat veiligheid voorop.

Meer informatie over de opdracht vind je in de [Project Brief](docs/getting-started/project_brief.md).

---

## Wat ga je doen

Zie ook de [Project Brief](docs/getting-started/project_brief.md) voor meer achtergrondinformatie. In het kort ga je aan de slag met:

1. **Infrastructuur ontwer**  
   Jullie gaan zelf een infrastructuur ontwerpen. Deze moet voldoen aan de eisen vanuit de opdrachtgever én aan algemeen geldende principes voor een goed en veilig ontwerp, samengevat in een framework.

2. **Infrastructuur realiseren**  
   Jullie gaan dit ontwerp vertalen naar een werkende realisatie van zowel hosts als andere network devices. Zorg dat het netwerkverkeer correct verloopt, stel test-procedures op, en voer deze uit.

3. **Infrastructuur analyseren**  
   Gebruik standaarden (CIS baseline, ISO27001, NIST-richtlijnen, etc.) om daadwerkelijke en potentiële beveiligingsproblemen te identificeren en te verhelpen. Vertaal de opgedane inzichten naar een risico-analyse met concrete aanbevelingen. 

4. **Bedrijfsprocessen analyseren**  
   Analyseer de organisatieprocessen die relevant zijn voor de applicatie. Besteed daarbij aandacht aan bedrijfsmatige veiligheidsrisico’s en hoe je die kunt minimaliseren.

5. **Software ontwerpen en realiseren**  
   Bouw een applicatie waarin verschillende soorten gebruikers kunnen samenwerken. De applicatie verwerkt ook data uit ten minste één niet-menselijke bron (bijvoorbeeld IoT of een publieke database).

6. **Gebruikersinteracties ontwerpen**  
   Benader het project vanuit het perspectief van de eindgebruiker. Voer gebruikersonderzoek uit, verzamel inzichten (eventueel via persona’s), en kom tot een goed onderbouwd ontwerp.

7. **Stakeholderanalyse uitvoeren**  
   Breng in kaart wie allemaal invloed heeft op (of last heeft van) jullie systeem en hoe dat de ontwerpkeuzes beïnvloedt. Doe hiervoor aanvullend onderzoek in externe bronnen.  
   Voor een praktische uitleg over het uitvoeren van een stakeholderanalyse kun je [dit artikel](https://knowledgebase.hbo-ict-hva.nl/2_professional_skills/toekomstgericht_organiseren/organisatorische_context/stakeholder_analyse/) uit de Knowledgebase raadplegen.

8. **Risicoanalyse uitvoeren**  
   Identificeer en verhelp cybersecurity-risico’s op zowel technisch als organisatorisch vlak. Beschrijf de maatregelen die je kunt nemen en de impact daarvan op de organisatie.

---

## Wat lever je op aan het einde van het semester
1. **Een gebruikersonderzoek**  
   - De behoeften van verschillende gebruikers, vertaald naar specifieke productvereisten, plus stakeholder analyse.
2. **Een infrastructuur-ontwerp**  
   - Verschillende opties gewogen op basis van functionele en niet-functionele eisen.
3. **Een werkende infrastructuur**  
   - Containers die logisch zijn verbonden volgens je ontwerp.
4. **Een applicatie-ontwerp**  
   - Voor de hele applicatie (architectuur, datamodel, enz.).
5. **Een werkende applicatie**  
   - Die voldoet aan de gestelde eisen én het applicatie-ontwerp.
6. **Een analyse van applicatie, infrastructuur en bedrijfsprocessen**  
   - Met een technisch onderbouwd risicoprofiel en aanbevelingen om risico’s te verhelpen.

---

## Ontwikkelomgeving
Jullie werken met een Docker Ontwikkelomgeving, waar je containers of proof-of-concept van je applicatie in draaiende houdt.\
Het doel is dat je niet alleen **bouwt**, maar ook begrijpt hoe de infrastructuur logisch en veilig is opgezet.

---

## User Stories
Voor dit semester zijn geen kant-en-klare user stories opgesteld. Je schrijft ze zelf, op basis van:
- Deze opdrachtomschrijving  
- De [Project Brief](docs/getting-started/project_brief.md)  
- De [Sprint Goals](docs/getting-started/sprint_goals.md)  
- De functionele en technische vereisten
- Gebruikersonderzoek (bijv. via [docs/templates/persona_template.md](docs/templates/persona_template.md))  
- Onderzoek naar best practices  
- De aangeleverde learning stories  

Elke user story bevat:
- **Acceptatiecriteria**: de voorwaarden waaraan de story moet voldoen.  
- **Taken**: wat er concreet moet gebeuren.  
- **Veiligheidscheck**: het testen van de securityaspecten bij oplevering.

Een **user story** schrijf je in het format:

> _Als … (soort gebruiker) wil ik … (feature/actie), zodat … (doel/voordeel)._  

Bijvoorbeeld:  
> _Als geregistreerd gebruiker wil ik mijn wachtwoord kunnen resetten, zodat ik opnieuw toegang tot mijn account kan krijgen._

Er is een user story template te vinden in `.gitlab/issue_templates` (of in [docs/templates/user_story_template.md](docs/templates/user_story_template.md)).

---

## Definition of Done (DoD)
De DoD is een set criteria waaraan je user story of taak moet voldoen om als voltooid te worden beschouwd:

**Algemeen**  
- Alle acceptatiecriteria van de user story zijn afgevinkt.  
- Je hebt volgens HBO-ICT werkstandaarden gewerkt (Agile, GitLab, sprint boards, sprint planning etc.).  
- Het werk is technisch gedocumenteerd in het Engels en relevant voor de doelgroep.

**UX/UI**  
- De webapplicatie werkt zowel op mobiele als op desktop-apparaten.  
- Het UX/UI-gedeelte volgt het [Think-Make-Check](docs/getting-started/TMC.md)-principe (TMC).

**Code**  
- De code is geschreven volgens de afgesproken standaarden.  
- De code is functioneel getest.  
- De code is getest op veiligheid.  
- De code is (peer)gereviewed.  
- De code is gedocumenteerd.  
- De beveiliging van opgeleverde features is getest en gedocumenteerd.

---

## Samenwerken in het team

- **Samenwerkingscontract**  
  Aan het begin van het semester stelt je team een samenwerkingscontract op, op basis van [dit format](docs/templates/samenwerkingscontract.md).

- **Scrum Rollen**  
  Aan het begin van elke sprint wijst het team een **Scrum Master** en een **Product Owner** aan. Elk teamlid vervult minimaal één keer zo’n rol. Lees het Knowledgebase-artikel over scrum rollen voor meer details over deze rollen.

- **Dagelijkse stand-up**  
  Haal aan het begin van de werkdag de nieuwste versie van het project op uit GitLab.  
  Aan het einde van de werkdag push je de nieuwste versie terug.  

---

### Samenwerken in GitLab
Om gestructureerd samen te werken:

1. Maak per user story een nieuw merge request (MR) aan via de knop "Create merge request".  
2. Update je local repository (`git fetch`, `git pull`), check de nieuwe branch uit en werk aan de user story (status op “doing”).  
3. Commit en push dagelijks of vaker.  
4. Klaar? Wijs de MR toe aan reviewer(s) (status op “verify”) en wacht op feedback.  
5. Verwerk feedback tot alle acceptatiecriteria en DoD-punten behaald zijn.  
6. Reviewer(s) keuren de MR goed en mergen deze; de user story sluit automatisch.

---

## Vakinhoudelijke Presentaties

Tijdens dit semester werk je aan een aantal **presentaties** over Cyber Security-onderwerpen. Deze presentaties vervangen sommige workshops/hoorcolleges, en geven je de kans om extra diepgang op te zoeken. 

- **Opdracht**  
  Je presenteert 10 minuten en beantwoordt vragen (5 minuten). Behandel de relevantie voor Cyber Security, koppel het aan de opdracht en benoem de belangrijkste (technische) inzichten.

- **Planning & Proactieve aanpak**  
  Vanaf sprint 3 kies je zelf een onderwerp (of stel je er één voor). De eerste presentatie bepaalt de docent om je op weg te helpen. Kies een lesmoment en vraag minstens één week van tevoren een go/no-go.

- **Ondersteunend materiaal**  
  - Een presentatie van 8 slides (exclusief voorblad).  
  - Een bronnenlijst per slide met betrouwbare bronnen (zie Knowledgebase voor bronvermelding). 

- **Onderwerpenlijst**  
  Voor sprint 2 en 3 krijg je een onderwerp toegewezen uit [de lijst met onderwerpen](docs/getting-started/onderwerpen.md). In sprints 4, 5, en 6 mag je zelf een onderwerp aandragen voor de presentaties. 

- **Beoordeling**  
  - Inhoud (onderwerp is uitgewerkt en relevant)  
  - Presentatievaardigheden (structuur, helderheid, visuele aantrekkingskracht)  
  - Interactie en reflectie (vragen en feedback verwerken)

Meer informatie in [de beschrijving van de vakinhoudelijke presentaties](docs/getting-started/Vakinhoudelijkepresentaties.md).

---

## Wanneer is jouw Secure Infrastructure klaar?

### Leeruitkomsten
Gedurende dit semester werk je aan **8 leeruitkomsten**, waarvan **7 algemeen** zijn en **1 specifiek** voor de Cyber Security‑leerroute:

1. **Software: Ontwerpen, Realiseren, Manage & Control**  
2. **Gebruikersinteractie: Analyseren, Ontwerpen, Realiseren**  
3. **Infrastructuur: Ontwerpen, Realiseren**  
4. **Persoonlijk Leiderschap**  
5. **Doelgericht Interacteren**  
6. **Onderzoekend Probleemoplossen**  
7. **Toekomstgericht Organiseren**  
8. **Cyber Security (Leerroutespecifiek)**

Een uitgebreide beschrijving van deze leeruitkomsten vind je hieronder of in de Learning Stories in GitLab.

#### 1. Software: Ontwerpen, Realiseren, Manage & Control
Je ontwerpt, maakt en test een (web)applicatie met een relationele database op basis van requirements en acceptatiecriteria. Je maakt, volgens geldende standaarden, gebruik van opmaaktalen en programmacode en past daarbij standaardmethodes en algoritmes toe voor het ontwerpen en realiseren van onderhoudbare software. Je werkt in een team, houdt technische documentatie bij en beheert het software-ontwikkelproces in GitLab met behulp van gangbare samenwerkingsafspraken.

#### 2. Gebruikersinteractie: Analyseren, Ontwerpen, Realiseren
Je verbetert de gebruikersinteractie van je product door UX-standaarden en best practices te gebruiken. Je doorloopt elke sprint een TMC-cyclus (Think–Make–Check) en test je product met de eindgebruiker. Je stelt eigen user stories op vanuit de opgedane inzichten en werkt deze uit in je product. Ondersteunend aan dit proces maak je prototypes en bespreek je ontwerpkeuzes met je stakeholders. Je documenteert het TMC-proces en presenteert de iteraties aan je product owner tijdens reviews.

#### 3. Infrastructuur: Ontwerpen, Realiseren
Je ontwerpt en realiseert een nieuwe infrastructuur op basis van de behoeften van de gebruiker. Je beschrijft welke informatie nodig is om deze infrastructuur goed op te zetten en legt uit waarom dit relevant is. De infrastructuur is volledig en correct beschreven (van topologie tot communicatieprotocollen) en je hebt testprotocollen opgesteld. Je toont aan dat de infrastructuur zowel op functionaliteit als veiligheid is getest.

#### 4. Persoonlijk Leiderschap
Je reflecteert op je eigen gedrag en hoe dit anderen in je team beïnvloedt. Je neemt verantwoordelijkheid voor je rol en betrekt je team actief bij je persoonlijke ontwikkeling door open te staan voor feedback. Je benoemt je sterke punten en ontwikkelpunten en gebruikt die inzichten voor een realistisch zelfbeeld als professional (o.a. voor oriëntatie op de arbeidsmarkt).

#### 5. Doelgericht Interacteren
Je communiceert schriftelijk en mondeling op een professionele manier met alle belanghebbenden. Je stemt behoeften en verwachtingen op elkaar af en presenteert resultaten én aanbevelingen volgens de daarvoor geldende kwaliteitsnormen. Je werkt bewust samen binnen de HvA HBO-ICT Agile/Scrum-methodiek, neemt verantwoordelijkheid voor jouw deel van het werk en houdt rekening met de teamdynamiek.

#### 6. Onderzoekend Probleemoplossen
Je onderzoekt mogelijke oplossingen voor een probleem of behoefte binnen het project. Je gebruikt betrouwbare bronnen om je keuzes te onderbouwen, stemt af met belanghebbenden en test de effectiviteit door veldonderzoek (bijv. gebruikerstesten, interviews). Je gebruikt de resultaten om je product te verbeteren en presenteert je bevindingen gestructureerd volgens een gegeven format.

#### 7. Toekomstgericht Organiseren
Je brengt de doelen, betrokkenen en aandachtspunten van de opdracht in kaart en past je kennis toe in het project. Hierbij volg je de afgesproken kwaliteitsnormen en bespreek je regelmatig risico’s en kansen met de belanghebbenden. Je beschrijft welke ethische en/of maatschappelijke normen en waarden relevant zijn voor je keuzes. Je verdeelt de opdracht in haalbare deeltaken en plant ze binnen de beschikbare tijd.

#### 8. Cyber Security (Leerroutespecifiek)
Je analyseert voor een organisatie een enkelvoudig cybersecurity-vraagstuk vanuit technische, procesmatige en organisatorische invalshoeken. Je brengt in kaart welke behoeften er spelen, welke gegevensstromen er zijn, en welke knelpunten er in de huidige situatie ontstaan. Daarbij maak je gebruik van bestaande security frameworks en standaarden. Deze inzichten verwerk je tot een overzichtelijke analyse die duidelijk maakt welke cybersecurity-risico’s er zijn.

---

## Bewijslast

### VRAAK

Het is belangrijk om voor elke leeruitkomst te voldoen aan VRAAK: Variatie, Relevantie, Authenticiteit, Actueel, en Kwantiteit. Dit houdt in dat je gedurende het semester (Actueel) voor elke (sub-)leeruitkomst meerdere stukken bewijs moet verzamelen (Kwantiteit), die bestaan uit verschillende soorten bewijs (Variatie), en waarvan je kan uitleggen hoe het bewijs levert voor de leeruitkomst (Relevantie). Daarnaast moet het bewijs herleidbaar zijn (Authenticiteit). In het geval van feedback moet er bewijs zijn dat je écht de feedback hebt ontvangen van die persoon. In het geval van een deliverable moet er bewijs zijn dat je het zelf hebt geschreven of ontwikkeld, in de vorm van een Gitlab Commit. Mocht bewijs of feedback niet te herleiden zijn, dan wordt het niet meegenomen in een voortgangsevaluatie of feedbackmoment.

### Kennis staat centraal

In dit project is het niet voldoende om iets werkends op te leveren; je moet inhoudelijke kennis hebben en concepten met elkaar kunnen vergelijken. Om dit te controleren zullen er tijdens formele feedback- en evaluatiemomenten, zoals vraag-en-antwoord sessies, 1-op-1 expert reviews, of voortgangsevaluaties, verklarende vragen worden gesteld. Zo kunnen docenten vragen om verduidelijking bij geschreven stukken, om de overwegingen achter design-keuzes, of om onderbouwing van bepaalde uitspraken. Deze vragen zullen niet te complex zijn, en zijn goed te beantwoorden als je begrijpt wat je hebt gedaan en geschreven, en je motivaties op het moment dat je die taken deed. Als uit de antwoorden op deze vragen niet voldoende naar voren komt dat je de vereiste achtergrondkennis beheerst, zal er geen feedback of beoordeling worden gegeven aan dat deel van het opgeleverde werk.

### Voortgangsevaluaties

Tijdens voortgangsevaluaties is het de bedoeling dat je beoordeeld wordt op je leerproces: het is een evaluatie of wat je tot dan toe hebt geleerd voldoet aan de leeruitkomsten voor het einde van het semester. Om dit te bewijzen ga je gedurende het semester feedback ophalen bij docenten, studentmentoren, en medestudenten. In het portfolio maak je duidelijk welke feedback je hebt ontvangen, en wat je met deze feedback hebt gedaan. 

Om een leeruitkomst te bewijzen in een voortgangsevaluatie moet je éérst een aantal keer feedback hebben opgehaald, en hebben geïmplementeerd in deliverables of taken. Een bewijs wat wordt aangeleverd zonder feedback wordt **niet** beoordeeld in de voortgangsevaluatie. 

---

## Lesmateriaal
- De concepten in deze opdracht vind je in de aangeleverde bronnen, de Knowledgebase, Teams/DLO-kanalen en videomateriaal.  
- Twee belangrijke boeken:  
  1. **Kurose, J.F. & Ross, K.W. (2022)**, *Computer Networking: A Top-Down Approach (8th edition)*. Pearson.  
  2. **Stallings, W. & Brown, L. (2018)**, *Computer Security: Principles and Practice (4th edition)*. Pearson.

Houd ook actuele cybernieuws bij via bijvoorbeeld:  
- [The Register](https://www.theregister.com/)  
- [404 Media](https://www.404media.co/)

---

## HBO-i
Binnen deze opdracht ligt de focus op de volgende **beroepstaken**:  
- Software ontwerpen (S-O) : niveau 1  
- Software realiseren (S-R) : niveau 1  
- Software manage & control (S-MC) : niveau 1  
- Gebruikersinteractie analyseren (G-I) : niveau 1  
- Gebruikersinteractie ontwerpen (G-O) : niveau 1  
- Gebruikersinteractie realiseren (G-R) : niveau 1  
- Infrastructuur analyseren (I-A): niveau 1  
- Infrastructuur ontwerpen (I-O): niveau 1  
- Infrastructuur realiseren (I-R): niveau 1  
- Bedrijfsprocessen analyseren (B-A): niveau 1  

Daarnaast werk je aan de volgende **professional skills**:  
- Persoonlijk leiderschap (PL)  
- Toekomstgericht organiseren (TO)  
- Doelgericht interacteren (DI)  
- Onderzoekend probleemoplossen (OP)

---

## Belangrijke Links en Sjablonen

1. **Project Brief**  
   [docs/getting-started/project_brief.md](docs/getting-started/project_brief.md)  

2. **Product Requirements**  
   [docs/getting-started/product_requirements.md](docs/getting-started/product_requirements.md)

3. **Sprint Goals**  
   [docs/getting-started/sprint_goals.md](docs/getting-started/sprint_goals.md)

4. **Vakinhoudelijke Presentaties**  
   [docs/getting-started/Vakinhoudelijkepresentaties.md](docs/getting-started/Vakinhoudelijkepresentaties.md)

5. **TMC (Think-Make-Check) Cyclus**  
   [docs/getting-started/TMC.md](docs/getting-started/TMC.md)

6. **Persona Template**  
   [docs/templates/persona_template.md](docs/templates/persona_template.md)

7. **Scrum Rollen**  
   [docs/getting-started/scrumrollen.md](docs/getting-started/scrumrollen.md)

8. **Samenwerkingscontract (format, interne link)**  
   [docs/templates/samenwerkingscontract.md](docs/templates/samenwerkingscontract.md)

9. **Learning Story Template**  
   [docs/templates/learning_story_template.md](docs/templates/learning_story_template.md)

10. **User Story Template**  
    [docs/templates/user_story_template.md](docs/templates/user_story_template.md)

---

[^1]: Alia Lingua Semper Sonat Melius inc., established 2024.
