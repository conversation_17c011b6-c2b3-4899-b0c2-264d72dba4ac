MySQL Workbench setup

1) Open MySQL Workbench and connect to your server.
2) File -> Open SQL Script, select `db/schema.sql` from this project.
3) Click the lightning bolt to execute. This creates the `physiowebapp` database with `users` and `appointments` tables.

Optional: seed an admin user by adding an INSERT into `users` with a bcrypt hash.

Connecting the Flask app

- Install a MySQL driver:
  - `pip install mysql-connector-python` (pure Python) or `pip install pymysql`.
- Add a connection string in your Flask app (example using PyMySQL):

```python
import pymysql

connection = pymysql.connect(
    host='localhost',
    user='root',
    password='your_password',
    database='physiowebapp',
    cursorclass=pymysql.cursors.DictCursor
)
```

- Replace CSV reads/writes with SQL queries. Start with authentication:
  - On register: `INSERT INTO users (fullname, email, password_hash) VALUES (%s, %s, %s)`
  - On login: `SELECT id, email, password_hash FROM users WHERE email=%s` and verify with bcrypt

Notes

- Table `appointments` is ready for future calendar integration if you decide to bring that back.
- All times are `DATETIME` in UTC recommended; convert to/from local time in the UI.

