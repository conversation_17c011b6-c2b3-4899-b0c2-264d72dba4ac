# `Titel van het rapport`

Teamnummer:
Opdracht:
Blok:

| Naam | Studentnummer |

| Versie | Datum | Wijzigingen |
|--------|----------|------------------------|
| v0.0 | 01-01-1970 | Initialisatie van het universum |

# Inleiding

De inleiding geeft een beknopt overzicht van de aanleiding, centrale vraag, en opzet van het document. Beantwoordt de volgende vragen in ca. 1 alinea per vraag:

- Voor welke organisatie schrijf je dit verslag?
- Wat is de (korte) aanleiding van dit verslag?
- Wat is het doel van dit verslag? (beschrijf dit, en vat het uiteindelijk samen in één centrale vraag)
- Welke onderwerpen komen ter sprake in dit verslag, en hoe dragen ze bij aan het bereiken van het doel van dit verslag?

**Tips**:
De antwoorden op deze vragen zijn (meestal) te vinden in een opdrachtomschrijving, project brief, of andere begeleidende documentatie. Mocht dit niet het geval zijn, ga dan in gesprek met opdrachtgevers om antwoord te krijgen.

# Context en Vereisten

Een organisatie zal nooit 'zomaar' vragen om een infrastructuur. Een IT-infrastructuur is een kostbare investering, en wordt (bijna) altijd gebruikt om bedrijfsprocessen te ondersteunen, of als product om te verkopen. Om een goed ontwerp te maken is het dus belangrijk om éérst in kaart te brengen welke processen of producten er met de infrastructuur worden ondersteund. De specifieke inrichting van dit proces, zoals hoe het verloopt, welke gebruikers in een organisatie het uitvoeren, en hoe vaak het wordt uitgevoerd, vormen de kaders voor het uiteindelijke ontwerp van de infrastructuur.

In dit hoofdstuk worden er twee aspecten van het ontwerp uitgelicht: de organisatorische context en de vereisten. De organisatorische context leidt tot een beschrijving van de processen die op de infrastructuur worden uitgevoerd, zoals klanten orders laten plaatsen, het verzamelen van relevante gegevens uit verschillende bronnen, of het aansturen van productie binnen een fabriek. Aan de hand van die beschrijvingen, én de gesprekken met of documentatie vanuit opdrachtgevers, kunnen vereisten worden opgesteld: functionele, technische, en juridische eisen die voorschrijven volgens welke regels de infrastructuur moet werken. 

## Organisatorische Context

Geef de context van het ontwerp. De context van de infrastructuur geeft de plaats van de infrastructuur binnen de organisatie aan. In de context staat een beschrijving van de organisatie of afdeling waar de infrastructuur wordt gebruikt, welke processen er worden gefaciliteerd of ondersteund door de infrastructuur, wie er gebruik gaat maken van de infrastructuur, en wat de doelstelling van de infrastructuur is. Vaak staat hier ook al hoe de infrastructuur moet worden gebruikt.

Beantwoordt de volgende vragen. Maak per vraag een paragraaf aan, bestaande uit meerdere alinea's:

- Voor welke organisatie wordt deze infrastructuur gebouwd? Wat zijn de belangrijkste activiteiten van deze organisatie? 
- Welke processen of activiteiten van deze organisatie worden ondersteund door de infrastructuur? Beschrijf deze activiteiten en/of processen tot in detail. Maak ook inzichtelijk hoe belangrijk de infrastructuur gaat zijn in het uitvoeren van van deze processen of activiteiten.
- Welke stakeholders zijn er bij deze infrastructuur, en wat zijn hun belangen? Denk hierbij aan klanten, opdrachtgevers, toezichthouders, of andere belanghebbenden.
- Wat is de doelstelling van de infrastructuur? Als deze infrastructuur gerealiseerd is, wat gaan de verschillende belanghebbenden hiervan merken? 

## Technische Context

Leg hier de belangrijkste technische infrastructuur-concepten uit. Geef aan wat dit concept inhoudt, waarom het relevant is voor het project wat de organisatie wilt uitvoeren, welke functionele vereisten ermee worden vervuld, en welke alternatieven er zijn om deze functionaliteit ook te implementeren.

## Vereisten, standaarden, en richtlijnen

Beschrijf de verschillende vereisten van de infrastructuur vanuit de opdrachtgever, en aan welke standaarden en richtlijnen de infrastructuur moet voldoen. Geef ten minste de volgende onderwerpen aan:

- Welke **functionele vereisten** er zijn.
- Welke **technische vereisten** er zijn.

Daarnaast kunnen de volgende onderwerpen relevant zijn:

- Welke **standaarden en richtlijnen** worden toegepast. Vaak hebben opdrachtgevers interne richtlijnen waaraan producten moeten voldoen, die ook van toepassing kunnen zijn binnen dit project. Daarnaast werken veel industrieën met standaarden: extern opgestelde richtlijnen die worden gebruikt als kwaliteitskeurmerk.
- Welke **wetten** van toepassing zijn op de infrastructuur, of de componenten.

**Tips:**

De mate waarin de infrastructuur voldoet aan de opgestelde functionele en technische vereisten bepaalt het succes van het project. Hoe beter de infrastructuur voldoet aan de gestelde vereisten, hoe tevredener een opdrachtgever zal zijn. Later in het verslag zullen deze vereisten worden gebruikt als basis voor de functionele en technische tests. Ga bij het schrijven van de vereisten na hoe 'testbaar' ze zijn, en denk na over hoe je deze zou testen. Het is beter om aan voorhand de vereisten aan te scherpen, dan naderhand te realiseren dat ze niet (makkelijk) verifieerbaar zijn.

# Infrastructuur

In het hoofdstuk Infrastructuur wordt de infrastructuur als geheel beschreven. Dit gaat in op de devices binnen de infrastructuur, hun functie binnen de infrastructuur, en de onderlinge verbanden. Dit leidt uiteindelijk tot een ontwerp waarin duidelijk wordt hoe de verschillende devices met elkaar verbonden zijn. 

## Inrichting

Op basis van de vereisten en richtlijnen kan een eerste inrichting van de infrastructuur worden gemaakt. In dit hoofdstuk worden éérst de benodigde diensten in kaart gebracht. Deze diensten worden geleverd door infrastructuur- of netwerk-applicaties zodat (bijvoorbeeld) devices verbonden kunnen worden, en informatie bij het juiste device terecht komt. Vervolgens moet in kaart worden gebracht welke devices er met de infrastructuur verbonden worden. Dit gaat niet alleen over hosts, maar ook over networking equipment. Dit alles wordt behandeld in twee paragrafen:

- Welke diensten in de infrastructuur aangeboden moeten worden aan hosts, zoals routing, address resolution, of gateways;
- Welke devices in de infrastructuur zitten, zoals hosts, routers, switches, of hubs, en de rol die ze vervullen in de infrastructuur.

## Ontwerp

Het ontwerp maakt inzichtelijk hoe de verschillende devices met elkaar verbonden zijn. Dit ontwerp wordt gemaakt op basis van de functionele vereisten en standaarden. Deze worden vertaald naar een netwerkontwerp, die zowel met tekst, tabellen, en visualisaties wordt ondersteund. Benoem in het ontwerp de volgende onderwerpen:

- Belangrijkste uitgangspunten of functionaliteiten van het ontwerp, op basis van de vereisten;
- Welke (range aan) addressen worden gebruikt om de verschillende hosts in de infrastructuur te identificeren;
- Eventuele genomen veiligheidsmaatregelen, en waar in de infrastructuur deze worden toegepast.

Leg bij het ontwerp ook uit welke alternatieven zijn overwogen, en waarom dit het juiste ontwerp is voor de gestelde vereisten. 

**Tips:**

Het ontwerpen van een goede infrastructuur kan lastig zijn. Hoofdstuk 1 van het boek *Cisco Certified Design Expert (CCDE 400-007) Official Cert Guide* van Zig Zsiga beschrijft de kernprincipes die ten grondslag liggen aan een goed ontwerp. Hou het vooral simpel: het ontwerp moet voldoen aan de gestelde functionele vereisten, niets meer. 

### Templates

Gebruik onderstaande template om de cruciale informatie over het ontwerp weer te geven. Zorg dat de informatie in de tabel overeenkomt met de informatie in het ontwerp.

| Device | Adres | Functie | Verbonden met |
|------------|--------------|-------------|----------------------|
| Naam van het device | Via welk adres is dit te bereiken? | Waarom is dit nodig in het netwerk? | Met welk(e) ander(e) device(s) is dit verbonden?|

# Systemen

In het hoofdstuk Systemen worden de individuele devices uit het voorgaande hoofdstuk verder beschreven. Er wordt gekeken naar de netwerk-applicaties die draaien op deze devices, het type netwerkverkeer wat wordt geïnitieerd of gefaciliteerd door deze systemen, en de te verwachten protocollen die worden gebruikt om te communiceren over het netwerk.

Beschrijf per systeem de volgende twee hoofdstukken. Mochten er protocollen zijn die op verschillende systemen identiek worden toegepast, benoem dit dan in het rapport. Het is niet nodig om het protocol opnieuw te beschrijven.

## Applicaties en instellingen

In dit hoofdstuk worden de individuele systemen in de infrastructuur beschreven. Per systeem wordt aangegeven welke netwerkapplicaties hierop draaien, en de verschillende instellingen die worden aangepast om het netwerkverkeer te faciliteren of initiëren. Geef daarnaast per applicatie aan welke protocollen worden gebruikt voor de communicatie, en wat voor soort berichten er verstuurd worden.

### Templates

Gebruik onderstaande template om per systeem in kaart te brengen welke applicaties er op draaien, welke protocollen ze gebruiken, en welke poort ze gebruiken.

| Applicatie | Poort | Protocollen |
|---------------|-------------|----------------------|
| Naam applicatie | Gebruikte poort nummer (indien van toepassing) | Gebruikte protocollen voor de communicatie |

Gebruik onderstaande template om makkelijk informatie over de instellingen weer te geven. Vul in de laatste kolom in waarom je voor die specifieke optie hebt gekozen, vergeleken met andere opties. Gebruik per applicatie een nieuwe tabel.

| Instelling | Mogelijke opties | Gekozen optie | Uitleg |
|------------|------------|-------------|-------------------------------|
| Naam instelling | Lijst met opties <br> één optie per regel | Gekozen optie | Waarom heb je die gekozen? Koppel het terug naar de functionele vereisten óf de functie van de applicatie op de infrastructuur. |

## Protocollen

In dit hoofdstuk worden de gebruikte protocollen nader toegelicht. Geef per protocol (globaal) aan wat de functie ervan is binnen de infrastructuur en hoe het technisch werkt. Mochten er nieuwe protocollen voor de applicatie geschreven zijn, geef dan aan hoe deze protocollen zijn opgesteld en hoe ze werken. 

# Realiseren

Op basis van het gekozen ontwerp kan de infrastructuur worden gerealiseerd.

## Gekozen technologie

Infrastructuur kan op een aantal manieren worden opgezet: zo is er de optie om alles on premise of in de cloud uit te voeren, en de keuze om fysieke hardware te gebruiken of de configuratie te virtualiseren. Beschrijf hier welke keuze er is gemaakt. Mochten er een aantal opties mogelijk zijn, vergelijk deze opties dan met elkaar en beargumenteer waarom de gekozen optie goed werkt voor de organisatie.

## Testen

Zodra duidelijk is hoe de infrastructuur moet werken, welke applicaties er op gaan draaien, én hoe het netwerkverkeer er uit ziet wat wordt gegenereerd door de infrastructuur, kunnen tests worden opgesteld voor de infrastructuur. Met tests wordt de correcte werking van de infrastructuur gecontroleerd. Het is belangrijk om twee situaties te testen: a) of de infrastructuur correct werkt, en b) of de infrastructuur geen onverwachte situaties toestaat. Beschrijf de tests zowel op infrastructuur-niveau als op systeem-niveau.

## Implementatieplan

Om de infrastructuur te implementeren is het belangrijk om een implementatieplan op te stellen. In het implementatieplan staat omschreven in welke stappen de infrastructuur wordt opgebouwd. Door de infrastructuur gefaseerd op te bouwen kunnen individuele onderdelen van de infrastructuur getest worden voordat er nieuwe onderdelen aan worden toegevoegd, en wordt duidelijk welke onderdelen sequentieel moeten worden uitgevoerd, en welke onderdelen serieel kunnen worden geïmplementeerd.

**Tip:**

Geef elke stap van het implementatieplan een eigen nummer. Dit maakt het makkelijker om aan te geven wanneer een bepaalde test moet worden uitgevoerd. Daarnaast is het verstandig om de correcte werking van een onderdeel van de infrastructuur te testen zodra dat onderdeel af is.