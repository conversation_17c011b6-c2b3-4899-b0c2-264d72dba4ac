# Docker Lab Verslag

**Naam:** Ali  
**Datum:** 15 september 2025

---

## Inleiding

In dit lab heb ik <PERSON><PERSON> geleerd. Docker helpt om programma's in containers te draaien. Ik heb een webserver en database gemaakt die met elkaar kunnen praten.

---

## 1. Docker Containers

**Vraag: Wat is het verschil tussen een container en een Virtual Machine?**

**Container:**

- <PERSON><PERSON><PERSON><PERSON><PERSON>, start heel snel 
- Gebruikt de kernel van het besturingssysteem van de host
- Vraagt relatief weinig geheugen en opslag

**Virtual Machine (VM):**

- Zwaarder en trager om op te starten
- Draait een volledig eigen besturingssysteem met kernel
- Heeft veel meer geheugen en CPU nodig

**Wanneer inzetten:**

- **VM:** Als je echt een heel ander OS nodig hebt of legacy software moet draaien
- **Container:** Als je moderne applicaties wilt bouwen, testen en uitrollen met weinig overhead

### Taak 1.1: Container starten

Commando:

`docker run -it ubuntu bash`

Wat gebeurt er:

- Docker haalt Ubuntu op (als het er nog niet is)
    
- Er start een nieuwe container
    
- Ik krijg een terminal in de container
    

**Hoe weet je dat je in een container zit:**

- De naam in de terminal is raar (zoals `root@a1b2c3d4e5f6`)
    
- Er staan alleen Ubuntu-bestanden
    
- Je bent automatisch de admin (root)
    

**Commando’s testen:**

`ls          # Laat bestanden zien pwd         # Laat huidige map zien: / whoami      # Laat gebruiker zien: root`

**Container loskoppelen:**  
Druk `Ctrl+P` en dan `Ctrl+Q` om uit de container te gaan zonder hem te stoppen.

**Containers bekijken:**

`docker ps       # Draaiende containers docker ps -a    # Alle containers (ook gestopte)`

**Container stoppen en verwijderen:**

`docker stop [CONTAINER_ID]    # Container stoppen docker rm [CONTAINER_ID]      # Container verwijderen`

**Verschil stoppen vs verwijderen:**

- **Stoppen:** Container pauzeert, data blijft
    
- **Verwijderen:** Container weg, alles kwijt
    

---

## 2. Docker Images

### Taak 2.1: Images downloaden

Image ophalen:

`docker pull python:3.11-alpine`

Wat gebeurt er: Docker haalt Python op van Docker Hub. Alpine is een kleine Linux-versie, dus de download is sneller.

**Lagen:** Elke regel die je ziet is een "laag". Docker kan lagen hergebruiken, waardoor builds sneller gaan.

**Images bekijken:**

`docker images`

**Docker Hub veiligheid:**

- **Officiële images:** Gemaakt en onderhouden door Docker of softwaremakers (veilig).
    
- **Community images:** Door anderen gemaakt (kan risico’s hebben).
    

**Risico’s:**

- Kwaadaardige code
    
- Ongepatchte software
    
- Slechte configuratie
    

**Veilig blijven:**

- Gebruik officiële images
    
- Controleer de beschrijving, populariteit en laatste updates
    

### Taak 2.2: Flask-app maken

Map maken:

`mkdir flask-app cd flask-app`

**app.py (webserver):**

`from flask import Flask  app = Flask(__name__)  @app.route('/') def hello():     return "Hello, World!"`

**requirements.txt:**

`flask mysql-connector-python`

### Taak 2.3: Dockerfile maken

Dockerfile:

`FROM python:3.11-alpine WORKDIR /app COPY . /app RUN pip install -r requirements.txt CMD ["python", "-m", "flask", "--app", "app", "run", "--host=0.0.0.0", "--port=80", "--debug"]`

**Container maken en starten:**

`docker build -t flask-app . docker run -d -p 5000:80 flask-app`

**Waarom requirements.txt apart?**

- Docker kan sneller bouwen als dit bestand niet verandert
    
- Makkelijk te delen met anderen
    
- Geeft overzicht welke pakketten nodig zijn
    

**Dockerfile uitleg:**

1. Begin met Python image
    
2. Ga naar `/app` map
    
3. Kopieer bestanden
    
4. Installeer dependencies
    
5. Start de webserver
    

**Code aanpassen zonder opnieuw bouwen:**  
Ja, met volumes kun je lokale bestanden koppelen.

---

## 3. Docker Networks - Containers laten praten

### Taak 3.1: Netwerk maken

`docker network create my_network docker network ls`

**Database starten:**

`docker run -d --name db --network my_network -e MYSQL_ROOT_PASSWORD=mysecretpassword mysql:8.0`

**Ubuntu container in hetzelfde netwerk:**

`docker run -it --rm --network my_network ubuntu bash`

**MySQL client installeren:**

`apt update && apt install -y mysql-client mysql -h db -u root -p`

**Waarom werkt ‘db’?**  
Docker DNS zorgt dat de servicenaam automatisch vertaald wordt naar het juiste IP.

---

## 4. Docker Volumes - Data bewaren

**Vraag: Wat gebeurt er met data als container stopt?**

- Zonder volume verdwijnt data bij verwijderen.
    

**Bind mount voor Flask-app:**

`docker run -d -p 5000:80 -v "$(pwd)"/flask-app:/app flask-app`

**Volume voor database:**

`docker volume create mysqldata docker run -d --name mysql-db --network my_network -e MYSQL_ROOT_PASSWORD=mysecretpassword -v mysqldata:/var/lib/mysql mysql:8.0`

---

## 5. Docker Compose

**compose.yml:**

`version: '3' services:   web:     build: ./flask-app     ports:       - "5000:80"     volumes:       - ./flask-app:/app     depends_on:       - db     networks:       - my_network    db:     image: mysql:8.0     environment:       MYSQL_ROOT_PASSWORD: mysecretpassword     volumes:       - mysqldata:/var/lib/mysql     networks:       - my_network  networks:   my_network:  volumes:   mysqldata:`

**Alles starten:**

`docker compose up -d docker compose ps`

**Diagram:**

`Computer ├── Docker     ├── Netwerk (my_network)         ├── Web container (Flask) - poort 5000         └── Database container (MySQL)`

---

## Reflectie

**Moeilijk:** Netwerken was het lastigste. Containers vinden elkaar via namen, dat moest ik even begrijpen. Ook volumes vs bind mounts was verwarrend.

**Geleerd:**

- Containers zijn sneller dan VM’s
    
- Images bestaan uit lagen
    
- Netwerken zijn nodig voor samenwerking
    
- Volumes bewaren data
    
- Compose maakt alles makkelijker
    

**Toepassing:**

- Snel ontwikkelomgevingen opzetten
    
- Software testen
    
- Applicaties deployen
    

---

## Bronnen

- Docker Docs – Containers overview
    
- Docker Docs – Best practices for images
    
- MySQL Official Image – Docker Hub: mysql
    
- Flask Documentation – Quickstart