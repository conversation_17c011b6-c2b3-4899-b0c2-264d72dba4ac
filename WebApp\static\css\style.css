/*me<PERSON><PERSON><PERSON> gemaakt door chat*/

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    color: #1e1919;
    line-height: 1.5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}


.navbar {
    background: #ffffff;
    border-bottom: 1px solid #f7f5f3;
    padding: 20px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    font-size: 18px;
    font-weight: 600;
    color: #1e1919;
    text-decoration: none;
}

.nav-links {
    display: flex;
    gap: 32px;
    list-style: none;
}

.nav-link {
    color: #5c5856;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.15s ease;
}

.nav-link:hover {
    color: #6dad07;
}


.btn {
    padding: 12px 24px;
    border: 1px solid transparent;
    border-radius: 6px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.15s ease;
    line-height: 1.2;
}

.btn-primary {
    background: #6dad07;
    color: white;
    border-color: #6dad07;
}

.btn-primary:hover {
    background: #0052cc;
    border-color: #0052cc;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #6dad07;
    border-color: #6dad07;
}

.btn-outline:hover {
    background: #6dad07;
    color: white;
}


.hero {
    padding: 80px 0 120px 0;
    text-align: center;
}

.hero h1 {
    font-size: 64px;
    font-weight: 600;
    line-height: 1.1;
    color: #1e1919;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.hero p {
    font-size: 20px;
    color: #5c5856;
    margin-bottom: 40px;
    line-height: 1.4;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    margin-bottom: 60px;
}

.hero-buttons .btn {
    margin: 0 8px;
}


.services {
    padding: 80px 0;
    background: #f7f5f3;
}

.services h2 {
    font-size: 48px;
    font-weight: 600;
    color: #1e1919;
    margin-bottom: 16px;
    text-align: center;
    letter-spacing: -0.01em;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.service-card {
    background: white;
    padding: 40px;
    border-radius: 8px;
    border: 1px solid #f7f5f3;
    text-align: center;
}

.service-card h3 {
    font-size: 24px;
    font-weight: 600;
    color: #1e1919;
    margin-bottom: 16px;
}

.service-card p {
    color: #5c5856;
    line-height: 1.6;
}


.login-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    padding: 40px 0;
}

.login-card {
    max-width: 800px;
    margin: 0 auto;
    background: #ffffff;
    border: 1px solid #f7f5f3;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
}

.login-welcome {
    flex: 1;
    background: #1e1919;
    color: white;
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.login-welcome h1 {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 16px;
}

.login-welcome p {
    color: #a8a29e;
    line-height: 1.5;
}

.login-form {
    flex: 1;
    padding: 60px 40px;
}

.login-form h2 {
    font-size: 28px;
    font-weight: 600;
    color: #1e1919;
    margin-bottom: 8px;
}

.login-form p {
    color: #5c5856;
    margin-bottom: 32px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    color: #1e1919;
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-input {
    width: 100%;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    padding: 12px;
    font-size: 16px;
    transition: border-color 0.15s ease;
}

.form-input:focus {
    outline: none;
    border-color: #0061ff;
}

.demo-buttons {
    display: flex;
    gap: 8px;
    margin-top: 20px;
}

.demo-btn {
    flex: 1;
    background: #f7f5f3;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    padding: 8px;
    font-size: 12px;
    color: #1e1919;
    cursor: pointer;
}


/* Agenda styles - matching big version */
.agenda-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.agenda-header h2 {
    font-size: 36px;
    font-weight: 600;
    color: #1e1919;
    margin: 0;
}

.agenda-card {
    background: white;
    border: 1px solid #f7f5f3;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.card-header {
    background: #f7f5f3;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e5e5;
    font-weight: 600;
    color: #1e1919;
}

.card-body {
    padding: 0;
}

.table-responsive {
    overflow-x: auto;
}

.agenda-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.agenda-table th {
    background: #f8f9fa;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #1e1919;
    font-size: 14px;
    border-bottom: 1px solid #e5e5e5;
}

.agenda-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #f7f5f3;
    color: #5c5856;
    font-size: 14px;
    vertical-align: middle;
}

.agenda-table tbody tr:last-child td {
    border-bottom: none;
}

.agenda-table tbody tr:hover {
    background: #fafafa;
}

.status-confirmed {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.status-pending {
    background: #ffc107;
    color: black;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.status-other {
    background: #6c757d;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.empty-appointments {
    text-align: center;
    padding: 3rem 1.5rem;
    color: #5c5856;
}

.empty-appointments h5 {
    color: #1e1919;
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
}

.empty-appointments p {
    margin: 0;
    color: #6c757d;
}

.flash-messages {
    margin: 20px 0;
}

.flash {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 10px;
}

.flash-success {
    background: #e8f5e8;
    color: #059669;
    border: 1px solid #d1fae5;
}

.flash-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.flash-info {
    background: #eff6ff;
    color: #2563eb;
    border: 1px solid #dbeafe;
}




/* New exciting features styles */
.profile-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, #6dad07, #5a9006);
    color: white;
    border-radius: 12px;
}

.user-role {
    font-size: 1.1rem;
    opacity: 0.9;
    text-transform: capitalize;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #6dad07;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #6dad07;
}

.stat-label {
    color: #666;
    margin-top: 0.5rem;
}

.quote-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border-left: 4px solid #6dad07;
}

.daily-quote {
    font-style: italic;
    font-size: 1.1rem;
    color: #333;
    margin: 0.5rem 0 0 0;
}

.quick-actions {
    margin-bottom: 2rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
    border-color: #5a6268;
}

.btn-info {
    background: #17a2b8;
    color: white;
    border-color: #17a2b8;
}

.btn-info:hover {
    background: #138496;
    border-color: #138496;
}

.live-stats {
    background: #e8f5e8;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.booking-header {
    text-align: center;
    margin-bottom: 2rem;
}

.booking-form {
    max-width: 500px;
    margin: 0 auto;
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.booking-note {
    background: #e8f5e8;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
    border-left: 4px solid #6dad07;
}

.services-list {
    display: grid;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.service-item {
    background: #f7f5f3;
    padding: 12px 16px;
    border-radius: 6px;
    border-left: 3px solid #6dad07;
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

.booking-result {
    max-width: 500px;
    margin: 2rem auto;
}

.success-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 1.5rem;
    border-radius: 8px;
}

.popular-times {
    max-width: 500px;
    margin: 2rem auto;
    text-align: center;
}

.time-slots {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.time-slot {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.time-slot.popular {
    background: #ff6b6b;
    color: white;
}

.time-slot.available {
    background: #51cf66;
    color: white;
}

.time-slot.busy {
    background: #ffd43b;
    color: #333;
}

@media (max-width: 768px) {
    .hero h1 {
        font-size: 48px;
    }

    .services h2 {
        font-size: 36px;
    }

    .login-card {
        flex-direction: column;
    }

    .nav-links {
        display: none;
    }

    .appointment {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .time-slots {
        flex-direction: column;
        align-items: center;
    }
}
