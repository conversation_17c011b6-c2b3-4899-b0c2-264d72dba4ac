# Risico analyse LCH

- risico = kans x impact


# Inleiding

Leefstijlcentrum Haarlemmermeer (LCH) gebruikt veel digitale middelen in de dagelijkse werkzaamheden. Denk aan een online ledenadministratie, het bijhouden van gezondheidsgegevens en een digitale planning voor trainingen en afspraken.  
Deze digitalisering levert veel voordelen op, maar brengt ook risico’s met zich mee. Cybersecurity, het beschermen van computers, netwerken en gegevens, is daarom erg belangrijk voor LCH. Zonder goede beveiliging kan het centrum te maken krijgen met datadiefstal, hackpogingen of storingen in systemen.  
De gevolgen hiervan kunnen groot zijn:  
- Leden kunnen geen afspraken maken of trainingen volgen als systemen niet werken.  
- Gevoelige informatie, zoals medische gegevens, kan openbaar worden.  

Het beschermen van deze gegevens is niet alleen een kwestie van vertrouwen, maar ook een wettelijke verplichting. De **Algemene verordening gegevensbescherming (AVG)** verplicht organisaties om maatregelen te nemen en datalekken te voorkomen ([Digital Trust Center](https://www.digitaltrustcenter.nl), [KVK](https://www.kvk.nl)).  
Voor een organisatie als LCH, die werkt met gezondheidsinformatie van klanten, is dit extra belangrijk. Gezondheidsgegevens vallen onder *bijzondere persoonsgegevens* en moeten volgens de wet zeer zorgvuldig worden behandeld ([Autoriteit Persoonsgegevens](https://www.autoriteitpersoonsgegevens.nl), z.d.).  

Een datalek kan leiden tot:  
- Reputatieschade  
- Boetes  
- Verminderd vertrouwen van klanten  
**Kortom:** cybersecurity is onmisbaar voor LCH om de dienstverlening draaiende te houden, de privacy van leden te beschermen en te voldoen aan de wet- en regelgeving ([KVK, 2025](https://www.kvk.nl)).  
In deze risicoanalyse kijken we daarom vooral naar het risico van datalekken en privacyschendingen bij LCH en hoe dit risico beperkt kan worden.


# Risicotabel

| Nr. | Risico (wat kan er misgaan) | Kans | Impact | Uitleg | Oplossing / Maatregelen |
|----|-----------------------------|------|--------|--------|-------------------------|
| 1 | Persoonsgegevens (zoals naam, e-mail of gezondheidsinfo) lekken uit | Hoog | Hoog | LCH slaat veel gevoelige gegevens op. Als dit uitlekt kan dat leiden tot identiteitsfraude, boetes en verlies van vertrouwen. | Data versleutelen, rechten beperken, privacybeleid, duidelijk plan voor datalekken. |
| 2 | Bedrijf kan niet verder werken door ransomware (gijzelsoftware) | Middel | Hoog | Hackers kunnen systemen blokkeren totdat er betaald wordt. Herstel kost veel tijd en geld. | Back-ups maken en testen, updates uitvoeren, antivirus, noodplan. |
| 3 | Hackers krijgen toegang via phishing | Hoog | Hoog | Medewerkers kunnen per ongeluk inloggegevens weggeven via nep-mails. | Training, multi-factor login, spamfilters, meldprotocol. |
| 4 | Slimme fitnessapparaten of wearables lekken data | Middel | Middel | Apparaten die verbonden zijn met internet kunnen worden misbruikt als ze niet goed beveiligd zijn. | Regelmatig updaten, veilige netwerken gebruiken, data versleutelen. |
| 5 | Problemen bij leveranciers (cloud of software) zorgen voor uitval | Middel | Hoog | Als een externe partij gehackt wordt of een storing heeft, ligt LCH ook stil. | Goede afspraken (contracten), betrouwbare leveranciers kiezen. |
| 6 | Menselijke fouten (bv. mail naar verkeerde persoon, laptop kwijt) | Hoog | Middel | De meeste datalekken ontstaan door onoplettendheid. | Training, duidelijke regels, apparaten versleutelen. |
| 7 | Slecht imago na een incident | Middel | Hoog | Negatief nieuws kan ervoor zorgen dat leden vertrekken of geen vertrouwen meer hebben. | Transparant communiceren, goed crisisplan. |
| 8 | Boete door schending van de AVG (privacywet) | Middel | Hoog | Als LCH de regels niet volgt (bv. datalek niet melden), kan dit leiden tot hoge boetes. | Privacybeleid, datalekken melden, DPIA uitvoeren bij nieuwe systemen. |
| 9 | Internet of netwerk valt uit | Middel | Middel | Zonder internet kan de planning of administratie niet doorgaan. | Reserveverbinding, offline kopie van planning. |
| 10 | Fysieke diefstal van laptops of apparatuur | Laag | Middel | Bij diefstal kan data op straat komen te liggen. | Toegang tot gebouw beveiligen, apparaten versleutelen, camera’s. |
| 11 | Verlies van gegevens door geen of slechte back-ups | Middel | Hoog | Zonder back-up kan data definitief kwijt zijn bij storing of fout. | Regelmatig back-ups maken en testen. |
| 12 | (Ex-)medewerker misbruikt toegang | Laag | Hoog | Een ontevreden medewerker kan gegevens kopiëren of lekken. | Toegangsrechten beperken, direct intrekken bij vertrek, logbestanden controleren. |
