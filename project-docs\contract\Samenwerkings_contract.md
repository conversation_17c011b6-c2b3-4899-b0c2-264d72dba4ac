# Samenwerkingscontract

**1 Doelstelling van het project en code of conduct**

**Gedragscode (Code of Conduct)**

Om een productieve en professionele samenwerking te waarborgen, committeren alle teamleden zich aan de volgende gedragsregels:

* **Professionele Werkhouding:**  
   Elk teamlid toont een actieve en betrokken werkhouding en streeft naar een wekelijkse studiebelasting van 40 uur.

* **Dagelijkse Stand-ups:**  
  Dagen op school, aan het begin van de les. Voor de dagen dat we thuis werken of online les hebben stuurt het teamlid voor het beginnen aan werk een bericht via de whatsapp groep.   
   Elk teamlid beantwoordt de drie standaard Scrum-vragen:

   1\. Wat heb ik gisteren gedaan?

   2\. Wat ga ik vandaag doen?

   3\. Zijn er obstakels of dingen waar je tegenaan bent gelopen?

* **Tijdige Oplevering:**  
   <PERSON><PERSON> op<PERSON>, taken en Scrum-activiteiten worden afgerond vóór de afgesproken deadline.

* **Iteratief Werken:**  
   We werken in Scrum-sprints van drie weken en pakken dagelijks minimaal één nieuwe User Story op en ronden er ook minimaal één af.

* **User stories:**  
   We maken de user stories niet al te uitgebreid via een vastgestelde template en laten de user stories na het maken controleren door een andere groepsgenoot zodat deze de user story nog meer zou kunnen opbreken.

* **Design en Software Ontwerp:**  
   Ontwerpen, plannen en overleggen gaan vooraf aan implementatie.

* **Git Versiebeheer:**  
   Code wordt op gestructureerde wijze beheerd met feature branches, pull/merge requests, code reviews en merge reviews.

* **Branches en user stories formulieren:**  
   Feature branches worden geformuleerd als: feature/”De feature” En user stories worden geformuleerd als: als … wil ik dat … zodat …

* **Feedback en Samenwerking:**  
   Teamleden geven en ontvangen tijdig constructieve feedback om de samenwerking te verbeteren. Verder is er de Retrospective om het samenwerkingsproces elke Sprint te verbeteren.

* **Conflictresolutie:**

  * Bij een meningsverschil proberen betrokken teamleden eerst onderling een oplossing te vinden.  
  * Lukt dit niet binnen één werkdag, dan wordt het besproken tijdens de eerstvolgende teamvergadering.  
  * Als het conflict na de teamvergadering niet is opgelost, wordt een docent ingeschakeld.  
  * Structurele of ernstige samenwerkingsproblemen kunnen leiden tot herverdeling van taken of uitsluiting van groepswerk.  
* **Afwezigheid en Verantwoordelijkheid:**  
   Verwachte afwezigheid wordt vooraf gemeld in het team en in de Scrum-meetings.

**Doelstelling project:** 

**2 Teamleden en Contactinformatie**

Chiel Slot \-<EMAIL> <EMAIL> 0630924662

Nubia Hunting \- \- <EMAIL> & <EMAIL> \- 06 27175175

Anthony ter Horst \- \- <EMAIL> & <EMAIL> \- 06 14602144

Tim loeffen – [<EMAIL>](mailto:<EMAIL>) \- 0640639229 ;)

Ali Nassiry \- [<EMAIL>](mailto:<EMAIL>) \- 0640754238 

Miguel van der Meer \- [<EMAIL>](mailto:<EMAIL>) \- 0617186650 

**3 Teamproces**

**3.1 Belbin rollen en sterke en zwakke punten**

|  | Ali | Nubia | Chiel | Miguel | Anthony | Tim |
| ----- | ----- | ----- | ----- | ----- | :---- | :---- |
|  |  |  |  |  |  |  |
| Voornaamste Belbin rol | De Plant.  | De Plant. | De Plant. | Crazy Dave | De Plant. | De Plant. |
| Sterke punten | communicatie  houden aan deadlines r | communiceren organiseren | doorwerken, comunicatie | kan naar 10 tellen  | communicatie,  | Design, software |
| Zwakke punten |  | plannen | samenwerken | feedback vragen | aan planning houden | Confrontaties aangaan |

*   
  Welke rollen zijn in jullie team sterk vertegenwoordigd? Wat is hiervan het voordeel? En wat is het risico?

* Welke rollen zijn in jullie team weinig vertegenwoordigd? Wat is hiervan het risico?

* Bedenk n.a.v. de Belbin rollen maatregelen om de samenwerking te verbeteren

**3.1 Verantwoordelijkheden/Rollen in het project**

* Scrum master: verandert per sprint zodat iedereen gelijke hoeveelheid tijd scrum master kan zijn

* Als iemand vragen heeft of ergens niet uitkomt zal deze dit bij de andere van de groep melden, zodat er gezamenlijk naar een oplossing kan worden gezocht en niemand vast komt te zitten op een onderdeel.

**3.2 Proces & Communicatie**

* Stand ups

* Weekplanning: wanneer is iedereen gezamenlijk aan het werk en daarbij of live aanwezig of actief online zodat er altijd live gecommuniceerd kan worden?

|  | Maandag | Dinsdag | Woensdag | Donderdag | Vrijdag | Zaterdag | Zondag |
| ----- | ----- | ----- | ----- | ----- | ----- | ----- | ----- |
| chiel | 8:30-12 op school | 12-18 thuis in het dorp | 12-14:30 op school | 8:30-16 op school | 12-18 thuis in het dorp | \- | \- |
| Nubia | 8:30-12 op school | 11-17 thuis | 12-14:30 Op school | 8:30-16 op school | 11- rest thuis | \- | \- |
| Anthony | 8:30 \- 17:00 | 11:00 \- 17:00 thuis | 12:00-14:30 school | 8:30 \- 16 school | 11-17:00 thuis |  |  |
| Miguel | 8:30 \- 12:00 | 10:00 \- 16:00 thuis | 12:00-14:30 school | 8:30 \- 16 school | 11-17:00 thuis | \- | \- |
| Tim | 08:30 \- 12  | 14:00 \- 17:00 | 12:00 \- 14:30 | 08:30 \- 16:10 | Kan verschillen 11 \- 17 | \- | \- |
| Ali | 8:30 \- 12:00 | thuis  6:00 \- 24:00  | 12:00 \- 14:30 | 8:30 \- 16:00  | thuis  8:30 \- 18:00 | indien nodig of tijd vrij | indien nodig of tijd vrij |

*   
  We communiceren (voornamelijk via whatsapp en on campus)

  * via whatsapp voor online dagen

  * via fysiek voor fysieke dagen

* Indien je kan reageren reageer dan op de vraag. Als een vraag specifiek naar iemand is gericht, maar jij weet het antwoord ook op de vraag, kan je deze vraag ook beantwoorden. Indien er een vraag specifiek aan jou is gesteld en je kan niet reageren, geef dan een reden waarvoor indien mogelijk.

**3.3 Documentatie**

* Onze documentatie wordt opgeslagen in onze google docs folder, gitlab en iedereen houdt een logboek en een technisch logboek bij.

* We gebruiken de TMC, moscow en scrum.

* TMC, STARR voor reflectie.

**3.4 Conflictsignalering en \-oplossing**

Doel is om elkaar verantwoordelijk te houden en onze afspraken na te komen.

Wat wordt er gedaan als er een conflict dreigt op te komen over:

* Kwaliteit geleverd werk. Samen bekijken we wat beter kan en dan tot een oplossing komen. Dreigt het vaker te gebeuren dan moeten we de wortel van het probleem vinden en kijken hoe we dat gaan oplossen.

* Verantwoordelijkheden m.b.t. planning en deadlines. Indien iemand zijn geplande doel niet heeft gehaald is het belangrijk de reden te zoeken waardoor deze persoon zijn doel niet heeft gehaald. Als het probleem is gevonden wordt er samengewerkt aan een oplossing voor dit probleem zodat dit niet vaker gebeurt.

* Communicatie, bereikbaarheid. Kijken waarom beschikbaarheid moeilijk is, proberen om elkaar te bereiken op andere manieren, niet storen uitzetten op je telefoon is al een goed begin.

* Waarschuwingen? Indien het veel vaker bij dezelfde persoon op hetzelfde gebied fout gaat wordt er nadat er overleg in het groepje is geweest hulp ingeschakeld van de docent. Op deze manier hopen we dan een oplossing te kunnen vinden.

* Gevolgen indien afspraken uit deze overeenkomst nog steeds niet worden nageleefd? Als na meerdere waarschuwingen en overleggen met onder andere de docent nog steeds geen verbetering komt, zal er een goed gesprek over hoe het voor de rest van het project zal moeten gaan.

* Termijnen wat betreft waarschuwingen en gevolgen? Als er problemen zijn worden deze bij de volgende stand-up geadresseerd en besproken om deze problemen op te lossen.

Als 1 van deze problemen dreigt zeer uit de hand te lopen dan zal de maatregel zijn om naar de docenten toe te gaan en via hen tot een oplossing te komen.

**3 Leerdoelen**

**Leerdoelen van ieder van de teamleden.**

Leerdoelen moeten SMART geformuleerd zijn en je geeft je teamleden feedback (niet alleen op SMART formulering, maar ook op het werken eraan waar mogelijk en relevant)

Ieder teamlid kiest voor dit samenwerkingscontract minstens één (1) leerdoel, waar je in dit project voortgang op wilt maken. Dat leerdoel is gericht op Prof skills, een tweede leerdoel kan ook meer technisch van aard zijn (maar zal vaak al in een learning story zitten dan)

* Voornaamste Leerdoel op de Professional Skills, SMART geformuleerd

* Voornaamste Leerdoel op de beroepstaken, SMART geformuleerd

Handtekeningen van de teamleden

| Naam teamlid | Handtekening teamlid |
| ----- | ----- |
| Ali | Ali \<3 |
| Miguel van der Meer | Miguel |
| Nubia Hunting | Nubia |
| Tim Loeffen | Tim |
| Anthony ter Horst | Anthony |
| Chiel Slot | ChielSlot |

