import os
import pymysql


def get_connection():
    """Create and return a MySQL connection to the physiowebapp database.

    Environment variables (optional overrides):
      DB_HOST, DB_USER, DB_PASSWORD, DB_NAME
    """
    host = os.getenv('DB_HOST', '127.0.0.1')
    user = os.getenv('DB_USER', 'root')
    password = os.getenv('DB_PASSWORD', 'Ajth')
    database = os.getenv('DB_NAME', 'physiowebapp')
    port = int(os.getenv('DB_PORT', '3306'))

    return pymysql.connect(
        host=host,
        user=user,
        password=password,
        database=database,
        port=port,
        cursorclass=pymysql.cursors.DictCursor,
        autocommit=True,
    )


__all__ = [
    'get_connection',
]


