# Sprint goals

The client, your company has not set specific user stories for this project.\
 So [sprint goals](https://www.scrum.org/resources/what-sprint-goal) they wish to see are missing.It is up to your team how to define sprint goals based upon your business analysis en your risk assessment.\
The sprint goals, that you wil define could be translated into [epics](https://www.scrum.org/resources/blog/what-are-epics-and-features) and Use case diagrams.

During the sprint review starting with Sprint review 2, you will need to motivate your decisions. Base on teh Sprint goals you are responsible to formulate.

Adherence to these sprint goals is part of the contract ALSSM has with their clients. Deviation from these standards is not allowed, unless with written permission from one of the supervisors.

Every sprint, the client would like to see a proof-of-concept of the application, so they can assess the feasibility and usability of the project within the organisation.\
Furthermore, they want to know how the security of the application and its infrastructure will be ensured and assessed.\
This includes, but is not limited to:\
*-* a list of standards that might apply to the project\
*-* potential countermeasures that follow from selected standards\
*-* security design principles that might apply\
*-* a testing methodology to assess the security status of the application and the infrastructure.

Please make this list more complete every Sprint Review.  