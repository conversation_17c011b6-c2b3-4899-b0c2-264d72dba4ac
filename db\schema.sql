-- MySQL schema for WebApp
-- Adjust database name/charset as needed
-- Create database (optional)
CREATE DATABASE IF NOT EXISTS physiowebapp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE physiowebapp;
-- Users table (replaces users.csv)
CREATE TABLE IF NOT EXISTS users (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT,
    fullname VARCHAR(150) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uq_users_email (email)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Appointments table (optional, for future scheduling features)
CREATE TABLE IF NOT EXISTS appointments (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    patient_name VARCHAR(150) NOT NULL,
    patient_email VARCHAR(255) NULL,
    patient_phone VARCHAR(50) NULL,
    therapist_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_appointments_therapist (therapist_id),
    CONSTRAINT fk_appointments_user FOREIGN KEY (therapist_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Seed admin user (adjust hash or remove if undesired)
-- INSERT INTO users (fullname, email, password_hash)
-- VALUES ('Admin', '<EMAIL>', '$2b$12$replace_with_bcrypt_hash');