from flask import Flask, render_template, request, redirect, url_for, flash, session
import bcrypt, csv, os

app = Flask(__name__)
app.secret_key = "hello"

@app.route("/")
def index():
    
    return render_template("index.html")

@app.route("/register", methods=["GET", "POST"])
def register():
    if request.method == "POST":
        fullname = request.form["fullname"]
        email = request.form["email"]
        password = request.form["password"]
        repeat_password = request.form["repeat_password"]

        if password != repeat_password:
            flash("Passwords do not match!", "danger")
            return redirect(url_for("register"))

        hashed_password = bcrypt.hashpw(password.encode("utf-8"), bcrypt.gensalt())

        with open("users.csv", "a", newline='') as file:
            writer = csv.writer(file)
            writer.writerow([fullname, email, hashed_password.decode('utf-8')])

        flash("Registration successful!", "success")
        return redirect(url_for("login")) 

    return render_template("register.html")


@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        email = request.form["email"]
        password = request.form["password"].encode('utf-8')

        with open("users.csv", "r") as file:
            reader = csv.reader(file)
            for row in reader:
                if row[1] == email and bcrypt.checkpw(password, row[2].encode('utf-8')):
                    session['email'] = email
                    flash("Login successful!", "success")
                    return redirect(url_for("dashboard"))

        flash("Invalid credentials!", "danger")
        return redirect(url_for("login"))

    return render_template("login.html")


@app.route("/dashboard")
def dashboard():
    if 'email' not in session:
        flash("Please log in to access the dashboard.", "warning")
        return redirect(url_for("login"))

    return render_template("dashboard.html")

@app.route("/logout")
def logout():
    session.pop('email', None)
    flash("Logged out successfully!", "success")
    return redirect(url_for("index"))







if __name__ == "__main__":
    app.run(debug=True)
