# Presentatie-onderwerpen

De eerste serie vakinhoudelijke presentaties gaat over de basisconcepten van netwerken.\
De volgende onderwerpen zijn beschik<PERSON>[^1]:

1. Wat is een netwerk?
2. Hosts en communication links.
3. CIA Triad.
4. TCP/IP Protocol stack.
5. Protocollen.
6. Applicatie laag.
7. Transport laag.
8. Netwerk laag.
9. Data link laag.
10. Domain Name System.
11. TCP en UDP.
12. Netwerk security: firewalls.
13. Applicatie-architecturen.
14. Routers, switches, en hubs.
15. Encryptie vs hashing.
16. Adressering: hostnames en ports. 
17. Adressering: IP- en MAC-adressen.
18. Segmentering, de DMZ, en NAT.
19. OWASP top 10: A03 Injection attacks & A10 Server Side Request Forgery.
20. OWASP top 10: A01 Broken Access Control & A07 Identification and Authentication Failures.
21. OWASP top 10: A04 Insecure Design & A05 Security Misconfiguration.
22. OWASP top 10: A06 Vulnerable and Outdated Components
23. OWASP top 10: A08 Software and Data Integrity Failures & A09 Security Logging and Monitoring Failures.
24. SOC & SIEM.
25. Public en Private Key Cryptography en de rol van Certificate Authorities.

Tijdens de tweede serie kunnen teams de volgende onderwerpen overwegen:

26. Standaarden en Richtlijnen: ISO 27001.
27. Standaarden en Richtlijnen: NIST 800-53.
28. Standaarden en Richtlijnen: de AVG.
29. IDS vs IPS systemen.
30. Threat actors model (Stallings hoofdstuk 1).
31. Man-in-the-Middle Attack.
32. Various ways to DDoS.
33. TLS & IPSec.
34. Mission Critical Activities.
35. Assets & Crown Jewels.
36. Downgrade attacks.

[^1]: De eerste onderwerpen zijn onderdeel van de fundamentele cybersecurity concepten.

Elk onderwerp uit onderstaande serie vakinhoudelijke onderwerpen is te groot voor één presentatie. 
Deze zijn bedoeld als inspiratie en geven een richting als je een presentatie wilt doen die niet op de lijsten staan en breder wilt denken.
Gebruik in dat geval onderstaande subonderwerpen als vertrekpunt, en onderzoek of je unieke invalshoeken vindt die passen bij jouw eigen interesses of binnen de context van jullie bedrijf kunnen vallen.

### 1. OWASP Top 10
- Wat zijn de meest kritieke kwetsbaarheden?
- Hoe kun je deze kwetsbaarheden opsporen en mitigeren?

### 2. NIS2-richtlijnen
- Wat zijn de belangrijkste doelen van NIS2?
- Hoe pas je deze richtlijnen toe in een specifieke sector?

### 3. ISO27001
- Hoe werkt het certificeringsproces?
- Welke stappen zijn essentieel voor implementatie?

### 4. Ethisch hacken
- Wat zijn de belangrijkste principes van ethisch hacken?
- Casus: De impact van een penetratietest op een organisatie.

### 5. Bekende ransomware-aanvallen
- Casus: Colonial Pipeline. (of een andere casus, in overleg)
- Hoe werd deze aanval uitgevoerd en welke lessen kunnen we trekken?

### 6. Geschiedenis van cybersecurity
- Hoe hebben computervirussen zich ontwikkeld?
- Belangrijke mijlpalen in de evolutie van cybersecurity.

### 7. Supply chain-aanvallen
- Casus: SolarWinds.
- Hoe beschermen organisaties hun supply chain?

### 8. Zero Trust Architecture
- Wat zijn de principes van Zero Trust?
- Hoe implementeer je dit in een organisatie?

### 9. Beveiliging van IoT-apparaten
- Wat zijn de grootste risico’s?
- Hoe kun je deze risico’s minimaliseren?

### 10. Quantum computing
- Wat zijn de implicaties voor encryptie?
- Hoe bereiden we ons voor op de impact van quantum computing?

### 11. GDPR in de praktijk
- Wat betekent de GDPR voor ontwikkelaars?
- Hoe verwerk je persoonsgegevens op een veilige manier?

### 12. Cybercrime-economie
- Hoe verdienen criminelen geld met cyberaanvallen?
- Wat zijn effectieve maatregelen tegen deze verdienmodellen?

### 13. AI in Cybersecurity
- Hoe wordt AI gebruikt om bedreigingen te detecteren?
- Wat zijn de risico’s van AI in verkeerde handen?

### 14. Injection-aanvallen
- Wat is een SQL-injectie en hoe werkt deze?
- Welke verdedigingstechnieken zijn effectief?

### 15. Broken Authentication
- Hoe werken Multi-Factor Authentication (MFA) en wachtwoordbeleid?
- Hoe herken je kwetsbaarheden in authenticatiesystemen?

### 16. Phishing-aanvallen
- Hoe worden phishing-aanvallen uitgevoerd?
- Wat kun je doen om gebruikers hiertegen te beschermen?

### 17. DNS Spoofing
- Hoe werkt DNS Spoofing technisch?
- Wat zijn de gevolgen en hoe kun je dit voorkomen?

### 18. Malware-analyse
- Welke tools gebruik je voor malware-analyse?
- Hoe kun je de werking van malware begrijpen en documenteren?

### 19. Encryptie-algoritmes
- Wat zijn de verschillen tussen symmetrische en asymmetrische encryptie?
- Wat is de rol van encryptie in moderne systemen?

### 20. Social engineering
- Wat zijn bekende methoden van social engineering?
- Hoe train je medewerkers om hier alert op te zijn?

> **Tip**: Combineer een onderwerp met een praktijkcasus om een unieke invalshoek te bieden. 